{"name": "dsk-desktop", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@mdi/font": "^7.4.47", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "vue": "^3.5.13", "vuetify": "^3.9.4"}, "devDependencies": {"@tauri-apps/cli": "^2", "@vitejs/plugin-vue": "^5.2.1", "vite": "^6.0.3"}}